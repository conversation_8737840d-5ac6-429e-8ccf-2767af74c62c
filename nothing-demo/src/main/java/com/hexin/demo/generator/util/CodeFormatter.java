package com.hexin.demo.generator.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.regex.Pattern;

/**
 * 代码格式化工具
 * 
 * <AUTHOR>
 */
@Slf4j
public class CodeFormatter {
    
    private static final Pattern MULTIPLE_BLANK_LINES = Pattern.compile("\n\\s*\n\\s*\n");
    private static final Pattern TRAILING_SPACES = Pattern.compile("[ \t]+$", Pattern.MULTILINE);
    private static final Pattern IMPORT_SECTION = Pattern.compile("(import\\s+[^;]+;\\s*\n)+");
    
    /**
     * 格式化Java代码文件
     */
    public static void formatJavaFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                log.warn("文件不存在: {}", filePath);
                return;
            }
            
            String content = new String(Files.readAllBytes(path), java.nio.charset.StandardCharsets.UTF_8);
            String formattedContent = formatJavaCode(content);

            if (!content.equals(formattedContent)) {
                Files.write(path, formattedContent.getBytes(java.nio.charset.StandardCharsets.UTF_8));
                log.debug("代码格式化完成: {}", filePath);
            }
        } catch (IOException e) {
            log.error("格式化文件失败: {}", filePath, e);
        }
    }
    
    /**
     * 格式化Java代码内容
     */
    public static String formatJavaCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return code;
        }
        
        // 移除行尾空格
        code = TRAILING_SPACES.matcher(code).replaceAll("");
        
        // 合并多个空行为单个空行
        code = MULTIPLE_BLANK_LINES.matcher(code).replaceAll("\n\n");
        
        // 格式化import语句
        code = formatImports(code);
        
        // 确保文件以换行符结尾
        if (!code.endsWith("\n")) {
            code += "\n";
        }
        
        return code;
    }
    
    /**
     * 格式化import语句
     */
    private static String formatImports(String code) {
        // 简单的import排序和去重
        String[] lines = code.split("\n");
        StringBuilder result = new StringBuilder();
        boolean inImportSection = false;
        java.util.Set<String> imports = new java.util.TreeSet<>();
        
        for (String line : lines) {
            String trimmedLine = line.trim();
            
            if (trimmedLine.startsWith("import ")) {
                inImportSection = true;
                imports.add(line);
            } else if (inImportSection && (trimmedLine.isEmpty() || !trimmedLine.startsWith("import "))) {
                // 输出排序后的import语句
                for (String importLine : imports) {
                    result.append(importLine).append("\n");
                }
                if (!trimmedLine.isEmpty()) {
                    result.append("\n");
                }
                inImportSection = false;
                imports.clear();
                result.append(line).append("\n");
            } else if (!inImportSection) {
                result.append(line).append("\n");
            }
        }
        
        // 处理文件末尾的import语句
        if (inImportSection) {
            for (String importLine : imports) {
                result.append(importLine).append("\n");
            }
        }
        
        return result.toString();
    }
    
    /**
     * 批量格式化目录下的所有Java文件
     */
    public static void formatDirectory(String directoryPath) {
        try {
            Path dir = Paths.get(directoryPath);
            if (!Files.exists(dir) || !Files.isDirectory(dir)) {
                log.warn("目录不存在或不是目录: {}", directoryPath);
                return;
            }
            
            Files.walk(dir)
                    .filter(path -> path.toString().endsWith(".java"))
                    .forEach(path -> formatJavaFile(path.toString()));
            
            log.info("目录格式化完成: {}", directoryPath);
        } catch (IOException e) {
            log.error("格式化目录失败: {}", directoryPath, e);
        }
    }
}
